.class public Lf/j/a/f/g$a;
.super Ljava/lang/Object;
.source ""


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lf/j/a/f/g;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x9
    name = "a"
.end annotation


# static fields
.field public static a:Ljava/lang/String; = "android.resource://com.ftsol.iptv/"

.field public static b:Ljava/lang/String; = "dns.php"

.field public static c:Ljava/lang/String; = "note.php"

.field public static d:Ljava/lang/String; = "raw/intro"

.field public static e:Ljava/lang/String; = "vpn.php"

.field public static h:Ljava/lang/String; = "sports.php"

.field public static i:Ljava/lang/String; = "com.MOD.cinemahd"


# direct methods
.method public static constructor <clinit>()V
    .locals 0

    return-void
.end method
